// Complete test of all functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testCompleteFlow() {
  console.log('🚀 Testing Complete Mental Health App Flow\n');
  
  try {
    // 1. Test User Signup and Login
    console.log('👤 TESTING USER FLOW');
    console.log('==================');
    
    // Test user login
    console.log('1️⃣ Testing user login...');
    const userLoginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ User login successful:', userLoginResponse.data.message);
    const userId = userLoginResponse.data.user._id;
    console.log(`📋 User ID: ${userId}`);
    
    // 2. Test AI Analysis
    console.log('\n🧠 TESTING AI ANALYSIS');
    console.log('======================');
    
    console.log('2️⃣ Testing AI analysis...');
    const aiAnalysisResponse = await axios.post(`${BASE_URL}/ai-analysis/analyze/${userId}`, {
      content: 'I feel very anxious about my upcoming presentation. My heart is racing and I cannot focus.'
    });
    console.log('✅ AI Analysis successful:', aiAnalysisResponse.data.message);
    console.log('📊 Mental Health Status:', aiAnalysisResponse.data.result.analysis.mentalHealthStatus.primary);
    console.log('📊 Sentiment:', aiAnalysisResponse.data.result.analysis.sentimentScore.label);
    console.log('📊 Risk Level:', aiAnalysisResponse.data.result.analysis.riskLevel.level);
    
    // 3. Test Therapist Flow
    console.log('\n👩‍⚕️ TESTING THERAPIST FLOW');
    console.log('============================');
    
    // Create a new test therapist
    console.log('3️⃣ Creating new therapist...');
    const newTherapist = {
      name: 'Dr. Test Therapist',
      email: '<EMAIL>',
      password: 'testpass123',
      specialty: 'Anxiety & Stress Management',
      location: 'New York, NY',
      phone: '******-TEST',
      experience: 5,
      coordinates: {
        type: 'Point',
        coordinates: [-74.0059, 40.7128] // NYC coordinates
      },
      hourlyRate: 150,
      bio: 'Experienced therapist specializing in anxiety and stress management.'
    };
    
    const createTherapistResponse = await axios.post(`${BASE_URL}/therapists`, newTherapist);
    console.log('✅ Therapist created successfully');
    const therapistId = createTherapistResponse.data._id;
    console.log(`📋 Therapist ID: ${therapistId}`);
    console.log(`📋 Approval Status: ${createTherapistResponse.data.isApproved}`);
    
    // Test therapist login before approval
    console.log('\n4️⃣ Testing therapist login before approval...');
    try {
      await axios.post(`${BASE_URL}/therapists/login`, {
        email: '<EMAIL>',
        password: 'testpass123',
        role: 'therapist'
      });
      console.log('❌ ERROR: Unapproved therapist was able to login!');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Therapist login correctly blocked before approval');
        console.log('✅ Message:', error.response.data.error);
      }
    }
    
    // 4. Test Admin Functions
    console.log('\n👨‍💼 TESTING ADMIN FUNCTIONS');
    console.log('=============================');
    
    // Get pending therapists
    console.log('5️⃣ Getting pending therapists...');
    const pendingResponse = await axios.get(`${BASE_URL}/therapists/admin/pending`);
    console.log(`✅ Found ${pendingResponse.data.count} pending therapists`);
    
    // Approve the therapist
    console.log('6️⃣ Approving therapist...');
    const approveResponse = await axios.put(`${BASE_URL}/therapists/${therapistId}/approve`, {
      adminId: userId,
      approved: true
    });
    console.log('✅ Therapist approved:', approveResponse.data.message);
    
    // Test therapist login after approval
    console.log('7️⃣ Testing therapist login after approval...');
    const therapistLoginResponse = await axios.post(`${BASE_URL}/therapists/login`, {
      email: '<EMAIL>',
      password: 'testpass123',
      role: 'therapist'
    });
    console.log('✅ Therapist login successful after approval:', therapistLoginResponse.data.message);
    
    // Test user blocking
    console.log('\n8️⃣ Testing user blocking...');
    const blockResponse = await axios.put(`${BASE_URL}/users/${userId}/block`, {
      isBlocked: true
    });
    console.log('✅ User blocked:', blockResponse.data.message);
    
    // Test blocked user login
    console.log('9️⃣ Testing blocked user login...');
    try {
      await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('❌ ERROR: Blocked user was able to login!');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Blocked user login correctly prevented');
        console.log('✅ Message:', error.response.data.error);
      }
    }
    
    // Unblock user
    console.log('🔟 Unblocking user...');
    const unblockResponse = await axios.put(`${BASE_URL}/users/${userId}/block`, {
      isBlocked: false
    });
    console.log('✅ User unblocked:', unblockResponse.data.message);
    
    // Test unblocked user login
    console.log('1️⃣1️⃣ Testing unblocked user login...');
    const finalLoginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ Unblocked user login successful:', finalLoginResponse.data.message);
    
    // 5. Test Database Persistence
    console.log('\n💾 TESTING DATABASE PERSISTENCE');
    console.log('===============================');
    
    console.log('1️⃣2️⃣ Checking data persistence...');
    const usersResponse = await axios.get(`${BASE_URL}/users`);
    const therapistsResponse = await axios.get(`${BASE_URL}/therapists`);
    
    console.log(`✅ Total users in database: ${usersResponse.data.length}`);
    console.log(`✅ Total therapists in database: ${therapistsResponse.data.length}`);
    
    // Clean up - delete test therapist
    console.log('\n🧹 CLEANUP');
    console.log('==========');
    console.log('1️⃣3️⃣ Cleaning up test data...');
    try {
      await axios.delete(`${BASE_URL}/therapists/${therapistId}`);
      console.log('✅ Test therapist deleted');
    } catch (error) {
      console.log('⚠️ Could not delete test therapist (may not have delete endpoint)');
    }
    
    console.log('\n🎉 COMPLETE FLOW TEST RESULTS');
    console.log('==============================');
    console.log('✅ User login/logout: WORKING');
    console.log('✅ AI Analysis: WORKING');
    console.log('✅ Therapist signup: WORKING');
    console.log('✅ Therapist approval system: WORKING');
    console.log('✅ Admin block functionality: WORKING');
    console.log('✅ Database persistence: WORKING');
    console.log('✅ MongoDB Atlas connection: WORKING');
    console.log('\n🚀 ALL FEATURES ARE WORKING PERFECTLY!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Error details:', error.response.data);
      console.error('📋 Status code:', error.response.status);
    }
  }
}

// Run the complete test
testCompleteFlow();
