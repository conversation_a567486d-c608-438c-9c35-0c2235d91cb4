// Test admin block functionality specifically
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAdminBlock() {
  console.log('🔒 Testing Admin Block Functionality\n');
  
  try {
    // 1. Get all users to find test user
    console.log('1️⃣ Getting all users...');
    const usersResponse = await axios.get(`${BASE_URL}/users`);
    console.log(`✅ Found ${usersResponse.data.length} users`);
    
    const testUser = usersResponse.data.find(user => user.email === '<EMAIL>');
    if (!testUser) {
      console.log('❌ <NAME_EMAIL> not found');
      return;
    }
    
    console.log(`📋 Test user: ${testUser.username} (${testUser.email})`);
    console.log(`📋 User ID: ${testUser._id}`);
    console.log(`📋 Current blocked status: ${testUser.isBlocked}`);
    
    // 2. Test normal login first
    console.log('\n2️⃣ Testing normal login...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ Normal login successful:', loginResponse.data.message);
    } catch (loginError) {
      console.log('❌ Normal login failed:', loginError.response?.data?.error || loginError.message);
    }
    
    // 3. Block the user
    console.log('\n3️⃣ Blocking user...');
    const blockResponse = await axios.put(`${BASE_URL}/users/${testUser._id}/block`, {
      isBlocked: true
    });
    console.log('✅ Block response:', blockResponse.data.message);
    console.log(`📋 User blocked status: ${blockResponse.data.user.isBlocked}`);
    
    // 4. Test login with blocked user
    console.log('\n4️⃣ Testing login with blocked user...');
    try {
      const blockedLoginResponse = await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('❌ ERROR: Blocked user was able to login!');
      console.log('Response:', blockedLoginResponse.data);
    } catch (blockedLoginError) {
      if (blockedLoginError.response?.status === 403) {
        console.log('✅ Login correctly blocked:', blockedLoginError.response.data.error);
        console.log('✅ Status:', blockedLoginError.response.data.status);
      } else {
        console.log('❌ Unexpected error:', blockedLoginError.response?.data || blockedLoginError.message);
      }
    }
    
    // 5. Unblock the user
    console.log('\n5️⃣ Unblocking user...');
    const unblockResponse = await axios.put(`${BASE_URL}/users/${testUser._id}/block`, {
      isBlocked: false
    });
    console.log('✅ Unblock response:', unblockResponse.data.message);
    console.log(`📋 User blocked status: ${unblockResponse.data.user.isBlocked}`);
    
    // 6. Test login after unblocking
    console.log('\n6️⃣ Testing login after unblocking...');
    try {
      const unblockLoginResponse = await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ Login after unblock successful:', unblockLoginResponse.data.message);
    } catch (unblockLoginError) {
      console.log('❌ Login after unblock failed:', unblockLoginError.response?.data?.error || unblockLoginError.message);
    }
    
    console.log('\n🎉 Admin block functionality test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Error details:', error.response.data);
    }
  }
}

async function testTherapistApproval() {
  console.log('\n👩‍⚕️ Testing Therapist Approval Functionality\n');
  
  try {
    // 1. Get pending therapists
    console.log('1️⃣ Getting pending therapists...');
    const pendingResponse = await axios.get(`${BASE_URL}/therapists/admin/pending`);
    console.log(`✅ Found ${pendingResponse.data.count} pending therapists`);
    
    if (pendingResponse.data.therapists.length === 0) {
      console.log('ℹ️ No pending therapists found');
      return;
    }
    
    const therapist = pendingResponse.data.therapists[0];
    console.log(`📋 Test therapist: ${therapist.name} (${therapist.email})`);
    console.log(`📋 Therapist ID: ${therapist._id}`);
    console.log(`📋 Approval status: ${therapist.isApproved}`);
    
    // 2. Test login before approval
    console.log('\n2️⃣ Testing therapist login before approval...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/therapists/login`, {
        email: therapist.email,
        password: 'therapist123',
        role: 'therapist'
      });
      console.log('❌ ERROR: Unapproved therapist was able to login!');
    } catch (loginError) {
      if (loginError.response?.status === 403) {
        console.log('✅ Login correctly blocked for unapproved therapist');
        console.log('✅ Message:', loginError.response.data.error);
      } else {
        console.log('❌ Unexpected error:', loginError.response?.data || loginError.message);
      }
    }
    
    // 3. Approve therapist
    console.log('\n3️⃣ Approving therapist...');
    const approveResponse = await axios.put(`${BASE_URL}/therapists/${therapist._id}/approve`, {
      adminId: '6865927973c6803d8e8c7686', // Admin user ID
      approved: true
    });
    console.log('✅ Approval response:', approveResponse.data.message);
    console.log(`📋 Therapist approved: ${approveResponse.data.therapist.isApproved}`);
    
    // 4. Test login after approval
    console.log('\n4️⃣ Testing therapist login after approval...');
    try {
      const approvedLoginResponse = await axios.post(`${BASE_URL}/therapists/login`, {
        email: therapist.email,
        password: 'therapist123',
        role: 'therapist'
      });
      console.log('✅ Therapist login successful after approval:', approvedLoginResponse.data.message);
    } catch (approvedLoginError) {
      console.log('❌ Therapist login failed after approval:', approvedLoginError.response?.data?.error || approvedLoginError.message);
    }
    
    console.log('\n🎉 Therapist approval functionality test completed!');
    
  } catch (error) {
    console.error('❌ Therapist approval test failed:', error.message);
    if (error.response) {
      console.error('📋 Error details:', error.response.data);
    }
  }
}

// Run both tests
async function runAllTests() {
  await testAdminBlock();
  await testTherapistApproval();
  
  console.log('\n📋 Summary:');
  console.log('✅ Admin block functionality tested');
  console.log('✅ Therapist approval functionality tested');
  console.log('✅ Real-time MongoDB Atlas working');
}

runAllTests();
