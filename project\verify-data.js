console.log('🔍 Verifying database data...');

const mongoose = require('mongoose');

async function verifyData() {
  try {
    console.log('Connecting to local MongoDB...');
    await mongoose.connect('mongodb://localhost:27017/mindease_local');
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Check users collection
    const users = await db.collection('users').find({}).toArray();
    console.log(`\n👥 Users found: ${users.length}`);
    users.forEach(user => {
      console.log(`  - ${user.username} (${user.email}) - Role: ${user.role}`);
    });
    
    // Check therapists collection
    const therapists = await db.collection('therapists').find({}).toArray();
    console.log(`\n👩‍⚕️ Therapists found: ${therapists.length}`);
    therapists.forEach(therapist => {
      console.log(`  - ${therapist.name} (${therapist.email}) - Specialty: ${therapist.specialty}`);
      console.log(`    Approved: ${therapist.isApproved}, Blocked: ${therapist.isBlocked}`);
    });
    
    // Check other collections
    const collections = ['appointments', 'journalentries', 'moods', 'payments', 'testresults'];
    for (const collectionName of collections) {
      const count = await db.collection(collectionName).countDocuments();
      console.log(`\n📊 ${collectionName}: ${count} documents`);
    }
    
    await mongoose.connection.close();
    console.log('\n✅ Database verification completed');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

verifyData();
