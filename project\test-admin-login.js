const axios = require('axios');

async function testAdminLogin() {
  console.log('🔐 Testing Admin Login Endpoint...\n');

  try {
    // Test admin login
    console.log('1. Testing admin login with correct credentials...');
    const response = await axios.post('http://localhost:3000/api/admin/login', {
      email: '<EMAIL>',
      password: 'MindEase@Admin2024'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ Admin login successful: ${response.status}`);
    console.log('Admin data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Admin login failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }

  try {
    // Test with wrong credentials
    console.log('\n2. Testing admin login with wrong credentials...');
    const response = await axios.post('http://localhost:3000/api/admin/login', {
      email: '<EMAIL>',
      password: 'wrongpassword'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('⚠️ This should have failed but succeeded:', response.status);

  } catch (error) {
    console.log('✅ Correctly rejected wrong credentials:', error.response?.status);
  }

  console.log('\n🎯 Admin login test completed!');
}

testAdminLogin();
