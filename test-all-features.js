// Comprehensive test of ALL features for evaluation
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_ID = '6865927973c6803d8e8c7686';

async function testAllFeatures() {
  console.log('🚀 COMPREHENSIVE FEATURE TEST FOR EVALUATION');
  console.log('==============================================\n');
  
  try {
    // 1. Test User Authentication
    console.log('👤 1. USER AUTHENTICATION');
    console.log('-------------------------');
    
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ User login: WORKING');
    console.log(`📋 User ID: ${loginResponse.data.user._id}`);
    
    // 2. Test AI Analysis & Sentiment
    console.log('\n🧠 2. AI ANALYSIS & SENTIMENT');
    console.log('------------------------------');
    
    const aiResponse = await axios.post(`${BASE_URL}/ai-analysis/analyze/${TEST_USER_ID}`, {
      content: 'I feel extremely anxious and depressed today. I cannot concentrate and feel hopeless.'
    });
    console.log('✅ AI Analysis: WORKING');
    console.log(`📊 Mental Health: ${aiResponse.data.result.analysis.mentalHealthStatus.primary}`);
    console.log(`📊 Sentiment: ${aiResponse.data.result.analysis.sentimentScore.label}`);
    console.log(`📊 Risk Level: ${aiResponse.data.result.analysis.riskLevel.level}`);
    console.log(`📊 Confidence: ${aiResponse.data.result.analysis.sentimentScore.confidence}%`);
    
    // Test real-time analysis
    const realtimeResponse = await axios.post(`${BASE_URL}/ai-analysis/realtime/${TEST_USER_ID}`, {
      content: 'I am feeling much better today and optimistic about the future!'
    });
    console.log('✅ Real-time AI Analysis: WORKING');
    console.log(`📊 Real-time Sentiment: ${realtimeResponse.data.result.analysis.sentimentScore.label}`);
    
    // 3. Test Journal & Mood Tracking
    console.log('\n📝 3. JOURNAL & MOOD TRACKING');
    console.log('------------------------------');
    
    // Create journal entry
    const journalResponse = await axios.post(`${BASE_URL}/journal`, {
      userId: TEST_USER_ID,
      content: 'Today was a challenging day but I managed to overcome my anxiety through breathing exercises.',
      mood: 'neutral',
      tags: ['anxiety', 'breathing', 'coping']
    });
    console.log('✅ Journal Entry: WORKING');
    console.log(`📋 Journal ID: ${journalResponse.data._id}`);
    
    // Get journal history
    const journalHistoryResponse = await axios.get(`${BASE_URL}/journal/user/${TEST_USER_ID}`);
    console.log('✅ Journal History: WORKING');
    console.log(`📋 Total Entries: ${journalHistoryResponse.data.length}`);
    
    // Test mood entry
    const moodResponse = await axios.post(`${BASE_URL}/mood`, {
      userId: TEST_USER_ID,
      mood: 'happy',
      intensity: 7,
      notes: 'Feeling good after therapy session'
    });
    console.log('✅ Mood Entry: WORKING');
    console.log(`📋 Mood: ${moodResponse.data.mood} (${moodResponse.data.intensity}/10)`);
    
    // 4. Test Therapist Management
    console.log('\n👩‍⚕️ 4. THERAPIST MANAGEMENT');
    console.log('-----------------------------');
    
    // Get all therapists
    const therapistsResponse = await axios.get(`${BASE_URL}/therapists`);
    console.log('✅ Get Therapists: WORKING');
    console.log(`📋 Total Therapists: ${therapistsResponse.data.length}`);
    
    // Get pending therapists
    const pendingResponse = await axios.get(`${BASE_URL}/therapists/admin/pending`);
    console.log('✅ Pending Therapists: WORKING');
    console.log(`📋 Pending Approval: ${pendingResponse.data.count}`);
    
    // 5. Test Admin Functions
    console.log('\n👨‍💼 5. ADMIN FUNCTIONS');
    console.log('----------------------');
    
    // Get all users
    const usersResponse = await axios.get(`${BASE_URL}/users`);
    console.log('✅ Get All Users: WORKING');
    console.log(`📋 Total Users: ${usersResponse.data.length}`);
    
    // Test user blocking (temporarily)
    const blockResponse = await axios.put(`${BASE_URL}/users/${TEST_USER_ID}/block`, {
      isBlocked: true
    });
    console.log('✅ User Blocking: WORKING');
    
    // Test blocked login
    try {
      await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('❌ ERROR: Blocked user could login!');
    } catch (error) {
      console.log('✅ Blocked Login Prevention: WORKING');
    }
    
    // Unblock user
    await axios.put(`${BASE_URL}/users/${TEST_USER_ID}/block`, {
      isBlocked: false
    });
    console.log('✅ User Unblocking: WORKING');
    
    // 6. Test Appointments
    console.log('\n📅 6. APPOINTMENT SYSTEM');
    console.log('------------------------');
    
    // Get appointments
    const appointmentsResponse = await axios.get(`${BASE_URL}/appointments`);
    console.log('✅ Get Appointments: WORKING');
    console.log(`📋 Total Appointments: ${appointmentsResponse.data.length}`);
    
    // 7. Test Database Persistence
    console.log('\n💾 7. DATABASE PERSISTENCE');
    console.log('--------------------------');
    
    // Test data persistence by getting analysis history
    const historyResponse = await axios.get(`${BASE_URL}/ai-analysis/history/${TEST_USER_ID}?limit=5`);
    console.log('✅ Analysis History: WORKING');
    console.log(`📋 Analysis Records: ${historyResponse.data.length}`);
    
    console.log('\n🎉 EVALUATION SUMMARY');
    console.log('=====================');
    console.log('✅ User Authentication: WORKING');
    console.log('✅ AI Analysis & Sentiment: WORKING');
    console.log('✅ Real-time Analysis: WORKING');
    console.log('✅ Journal & Mood Tracking: WORKING');
    console.log('✅ Therapist Management: WORKING');
    console.log('✅ Admin Functions: WORKING');
    console.log('✅ User Blocking: WORKING');
    console.log('✅ Appointment System: WORKING');
    console.log('✅ Database Persistence: WORKING');
    console.log('✅ MongoDB Atlas: WORKING');
    console.log('\n🚀 ALL FEATURES READY FOR EVALUATION!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Status:', error.response.status);
      console.error('📋 Error:', error.response.data);
    }
  }
}

// Run comprehensive test
testAllFeatures();
