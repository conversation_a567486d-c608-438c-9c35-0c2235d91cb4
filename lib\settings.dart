// import 'package:flutter/material.dart';

// class SettingsScreen extends StatelessWidget {
//   const SettingsScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Settings'),
//         backgroundColor: Colors.purple, // Set the app bar color to purple
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back),
//           onPressed: () {
//             Navigator.pop(context); // This will navigate back to the previous screen
//           },
//         ),
//       ),
//       body: Container(
//         decoration: const BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [
//               Color.fromRGBO(255, 226, 159, 1), // Light yellow
//               Color(0xFFFFC0CB), // Light pink
//             ],
//           ),
//         ),
//         child: Safe<PERSON>rea(
//           child: ListView(
//             padding: const EdgeInsets.all(16.0),
//             children: [
//               const Text(
//                 '',
//                 style: TextStyle(
//                   fontSize: 24,
//                   fontWeight: FontWeight.bold,
//                   color: Colors.purple,
//                 ),
//               ),
//               const SizedBox(height: 20),

//               ListTile(
//                 leading: const Icon(Icons.person, color: Colors.purple),
//                 title: const Text('Profile'),
//                 subtitle: const Text('Update your personal details'),
//                 onTap: () {
//                   // Navigate to profile update screen
//                 },
//               ),
//               const Divider(),

//               ListTile(
//                 leading: const Icon(Icons.notifications, color: Colors.purple),
//                 title: const Text('Notifications'),
//                 subtitle: const Text('Manage app notifications and reminders'),
//                 onTap: () {
//                   // Navigate to notifications settings
//                 },
//               ),
//               const Divider(),

//               ListTile(
//                 leading: const Icon(Icons.color_lens, color: Colors.purple),
//                 title: const Text('Theme'),
//                 subtitle: const Text('Choose light or dark mode'),
//                 onTap: () {
//                   // Navigate to theme settings
//                 },
//               ),
//               const Divider(),
//               ListTile(
//                 leading: const Icon(Icons.language, color: Colors.purple),
//                 title: const Text('Language'),
//                 subtitle: const Text('Select your preferred language'),
//                 onTap: () {
//                   // Navigate to language settings
//                 },
//               ),
//               const Divider(),

//               ListTile(
//                 leading: const Icon(Icons.lock, color: Colors.purple),
//                 title: const Text('Security'),
//                 subtitle: const Text('Manage PIN and biometric settings'),
//                 onTap: () {
//                   // Navigate to security settings
//                 },
//               ),
//               const Divider(),

//               ListTile(
//                 leading: const Icon(Icons.info, color: Colors.purple),
//                 title: const Text('About'),
//                 subtitle: const Text('Learn more about the app'),
//                 onTap: () {
//                   // Navigate to about screen
//                 },
//               ),
//               ListTile(
//                 leading: const Icon(Icons.help, color: Colors.purple),
//                 title: const Text('Support'),
//                 subtitle: const Text('Contact support or view FAQs'),
//                 onTap: () {
//                   // Navigate to support screen
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'dart:convert';
// import 'package:image_picker/image_picker.dart';
// import 'dart:io';

// class SettingsScreen extends StatefulWidget {
//   const SettingsScreen({super.key});

//   @override
//   _SettingsScreenState createState() => _SettingsScreenState();
// }

// class _SettingsScreenState extends State<SettingsScreen> {
//   final _formKey = GlobalKey<FormState>();
//   String username = '';
//   String email = '';
//   String role = '';
//   String password = '';
//   String confirmPassword = '';
//   File? _profileImage;
//   bool _isLoading = false;
//   String? _errorMessage;
//   String? _successMessage;

//   @override
//   void initState() {
//     super.initState();
//     // Fetch initial user data (mocked for now, replace with actual API call)
//     fetchUserData();
//   }

//   Future<void> fetchUserData() async {
//     // Replace with actual API call to fetch user data
//     setState(() {
//       username = 'current_user'; // Mocked data
//       email = '<EMAIL>';
//       role = 'user';
//     });
//   }

//   Future<void> _pickImage() async {
//     final picker = ImagePicker();
//     final pickedFile = await picker.pickImage(source: ImageSource.gallery);
//     if (pickedFile != null) {
//       setState(() {
//         _profileImage = File(pickedFile.path);
//       });
//     }
//   }

//   Future<void> updateUser() async {
//     if (!_formKey.currentState!.validate()) return;

//     setState(() {
//       _isLoading = true;
//       _errorMessage = null;
//       _successMessage = null;
//     });

//     try {
//       final response = await http.put(
//         Uri.parse('YOUR_API_BASE_URL/users/${username}'), // Replace with actual API URL
//         headers: {'Content-Type': 'application/json'},
//         body: jsonEncode({
//           'username': username,
//           'email': email,
//           'role': role,
//         }),
//       );

//       if (response.statusCode == 200) {
//         setState(() {
//           _successMessage = 'Profile updated successfully';
//         });
//       } else {
//         setState(() {
//           _errorMessage = jsonDecode(response.body)['message'] ?? 'Failed to update profile';
//         });
//       }
//     } catch (e) {
//       setState(() {
//         _errorMessage = 'An error occurred: $e';
//       });
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   Future<void> deleteUser() async {
//     setState(() {
//       _isLoading = true;
//       _errorMessage = null;
//       _successMessage = null;
//     });

//     try {
//       final response = await http.delete(
//         Uri.parse('YOUR_API_BASE_URL/users/${username}'), // Replace with actual API URL
//       );

//       if (response.statusCode == 200) {
//         setState(() {
//           _successMessage = 'Account deleted successfully';
//           // Optionally navigate away or reset form
//         });
//       } else {
//         setState(() {
//           _errorMessage = jsonDecode(response.body)['message'] ?? 'Failed to delete account';
//         });
//       }
//     } catch (e) {
//       setState(() {
//         _errorMessage = 'An error occurred: $e';
//       });
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Profile Settings'),
//         backgroundColor: Colors.purple,
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back),
//           onPressed: () {
//             Navigator.pop(context);
//           },
//         ),
//       ),
//       body: Container(
//         decoration: const BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [
//               Color.fromRGBO(255, 226, 159, 1),
//               Color(0xFFFFC0CB),
//             ],
//           ),
//         ),
//         child: SafeArea(
//           child: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Form(
//               key: _formKey,
//               child: ListView(
//                 children: [
//                   // Profile Picture
//                   Center(
//                     child: GestureDetector(
//                       onTap: _pickImage,
//                       child: CircleAvatar(
//                         radius: 50,
//                         backgroundImage: _profileImage != null ? FileImage(_profileImage!) : null,
//                         child: _profileImage == null
//                             ? const Icon(Icons.person, size: 50, color: Colors.purple)
//                             : null,
//                       ),
//                     ),
//                   ),
//                   const SizedBox(height: 10),
//                   Center(
//                     child: TextButton(
//                       onPressed: _pickImage,
//                       child: const Text(
//                         'Change Profile Picture',
//                         style: TextStyle(color: Colors.purple),
//                       ),
//                     ),
//                   ),
//                   const SizedBox(height: 20),

//                   // Username
//                   TextFormField(
//                     initialValue: username,
//                     decoration: const InputDecoration(
//                       labelText: 'Username',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.person, color: Colors.purple),
//                     ),
//                     onChanged: (value) => setState(() => username = value),
//                     validator: (value) {
//                       if (value == null || value.isEmpty) {
//                         return 'Please enter a username';
//                       }
//                       return null;
//                     },
//                   ),
//                   const SizedBox(height: 16),

//                   // Email
//                   TextFormField(
//                     initialValue: email,
//                     decoration: const InputDecoration(
//                       labelText: 'Email',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.email, color: Colors.purple),
//                     ),
//                     onChanged: (value) => setState(() => email = value),
//                     validator: (value) {
//                       if (value == null || value.isEmpty) {
//                         return 'Please enter an email';
//                       }
//                       if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
//                         return 'Please enter a valid email';
//                       }
//                       return null;
//                     },
//                   ),
//                   const SizedBox(height: 16),

//                   // Role
//                   DropdownButtonFormField<String>(
//                     value: role.isNotEmpty ? role : null,
//                     decoration: const InputDecoration(
//                       labelText: 'Role',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.work, color: Colors.purple),
//                     ),
//                     items: ['user', 'therapist'].map((String role) {
//                       return DropdownMenuItem<String>(
//                         value: role,
//                         child: Text(role),
//                       );
//                     }).toList(),
//                     onChanged: (value) => setState(() => role = value!),
//                     validator: (value) {
//                       if (value == null || value.isEmpty) {
//                         return 'Please select a role';
//                       }
//                       return null;
//                     },
//                   ),
//                   const SizedBox(height: 16),

//                   // Password
//                   TextFormField(
//                     decoration: const InputDecoration(
//                       labelText: 'New Password',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.lock, color: Colors.purple),
//                     ),
//                     obscureText: true,
//                     onChanged: (value) => setState(() => password = value),
//                     validator: (value) {
//                       if (value != null && value.isNotEmpty && value.length < 6) {
//                         return 'Password must be at least 6 characters';
//                       }
//                       return null;
//                     },
//                   ),
//                   const SizedBox(height: 16),

//                   // Confirm Password
//                   TextFormField(
//                     decoration: const InputDecoration(
//                       labelText: 'Confirm Password',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.lock, color: Colors.purple),
//                     ),
//                     obscureText: true,
//                     onChanged: (value) => setState(() => confirmPassword = value),
//                     validator: (value) {
//                       if (value != null && value.isNotEmpty && value != password) {
//                         return 'Passwords do not match';
//                       }
//                       return null;
//                     },
//                   ),
//                   const SizedBox(height: 20),

//                   // Error/Success Messages
//                   if (_errorMessage != null)
//                     Text(
//                       _errorMessage!,
//                       style: const TextStyle(color: Colors.red),
//                       textAlign: TextAlign.center,
//                     ),
//                   if (_successMessage != null)
//                     Text(
//                       _successMessage!,
//                       style: const TextStyle(color: Colors.green),
//                       textAlign: TextAlign.center,
//                     ),
//                   const SizedBox(height: 20),

//                   // Update Button
//                   ElevatedButton(
//                     onPressed: _isLoading ? null : updateUser,
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.purple,
//                       padding: const EdgeInsets.symmetric(vertical: 16),
//                     ),
//                     child: _isLoading
//                         ? const CircularProgressIndicator(color: Colors.white)
//                         : const Text(
//                             'Update Profile',
//                             style: TextStyle(color: Colors.white),
//                           ),
//                   ),
//                   const SizedBox(height: 16),

//                   // Delete Account Button
//                   OutlinedButton(
//                     onPressed: _isLoading
//                         ? null
//                         : () {
//                             showDialog(
//                               context: context,
//                               builder: (context) => AlertDialog(
//                                 title: const Text('Delete Account'),
//                                 content: const Text(
//                                     'Are you sure you want to delete your account? This action cannot be undone.'),
//                                 actions: [
//                                   TextButton(
//                                     onPressed: () => Navigator.pop(context),
//                                     child: const Text('Cancel'),
//                                   ),
//                                   TextButton(
//                                     onPressed: () {
//                                       Navigator.pop(context);
//                                       deleteUser();
//                                     },
//                                     child: const Text(
//                                       'Delete',
//                                       style: TextStyle(color: Colors.red),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             );
//                           },
//                     style: OutlinedButton.styleFrom(
//                       side: const BorderSide(color: Colors.red),
//                       padding: const EdgeInsets.symmetric(vertical: 16),
//                     ),
//                     child: const Text(
//                       'Delete Account',
//                       style: TextStyle(color: Colors.red),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:myapp/theme/app_theme.dart';
import 'package:provider/provider.dart';
import 'package:myapp/providers/user_provider.dart';
import 'package:myapp/services/stripe_service.dart';

import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Modern App Bar
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(AppTheme.radiusXL),
                    bottomRight: Radius.circular(AppTheme.radiusXL),
                  ),
                  boxShadow: AppTheme.mediumShadow,
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon:
                          const Icon(Icons.arrow_back_ios, color: Colors.white),
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    Expanded(
                      child: Text(
                        'Settings',
                        style: AppTheme.headingMedium.copyWith(
                          color: Colors.white,
                          fontSize: isSmallScreen ? 18 : 22,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  children: [
                    const SizedBox(height: AppTheme.spacingL),
                    _buildSettingsCard(
                      context,
                      icon: Icons.person,
                      title: 'Update Profile',
                      subtitle: 'Update your personal details',
                      onTap: () {
                        final userId =
                            Provider.of<UserProvider>(context, listen: false)
                                .userId;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                UpdateUserScreen(userId: userId),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.lock,
                      title: 'Change Password',
                      subtitle: 'Update your password',
                      onTap: () {
                        final userId =
                            Provider.of<UserProvider>(context, listen: false)
                                .userId;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                ChangePasswordScreen(userId: userId),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.payment,
                      title: 'Payment Methods',
                      subtitle: 'Manage your payment methods and billing',
                      onTap: () {
                        final userId =
                            Provider.of<UserProvider>(context, listen: false)
                                .userId;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                PaymentManagementScreen(userId: userId),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.notifications,
                      title: 'Notifications',
                      subtitle: 'Manage app notifications and reminders',
                      onTap: () {
                        // Navigate to notifications settings
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.color_lens,
                      title: 'Theme',
                      subtitle: 'Choose light or dark mode',
                      onTap: () {
                        // Navigate to theme settings
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.language,
                      title: 'Language',
                      subtitle: 'Select your preferred language',
                      onTap: () {
                        // Navigate to language settings
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.lock,
                      title: 'Security',
                      subtitle: 'Manage PIN and biometric settings',
                      onTap: () {
                        // Navigate to security settings
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.info,
                      title: 'About',
                      subtitle: 'Learn more about the app',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AboutScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildSettingsCard(
                      context,
                      icon: Icons.help,
                      title: 'Support',
                      subtitle: 'Contact support or view FAQs',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SupportScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  decoration: BoxDecoration(
                    color:
                        (iconColor ?? AppTheme.primaryColor).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor ?? AppTheme.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTheme.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingXS),
                      Text(
                        subtitle,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textLight,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class UpdateUserScreen extends StatefulWidget {
  final String userId;
  const UpdateUserScreen({super.key, required this.userId});

  @override
  _UpdateUserScreenState createState() => _UpdateUserScreenState();
}

class _UpdateUserScreenState extends State<UpdateUserScreen> {
  final _formKey = GlobalKey<FormState>();
  String username = '';
  String email = '';
  String role = '';
  File? _profileImage;
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    fetchUserData();
  }

  Future<void> fetchUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        setState(() {
          username = userData['username'] ?? '';
          email = userData['email'] ?? '';
          role = userData['role'] ?? 'user';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = jsonDecode(response.body)['message'] ??
              'Failed to fetch user data';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error fetching user data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _profileImage = File(pickedFile.path);
      });
    }
  }

  Future<void> updateUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final response = await http.put(
        Uri.parse('http://localhost:3000/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'email': email,
          'role': role,
        }),
      );

      if (response.statusCode == 200) {
        setState(() {
          _successMessage = 'Profile updated successfully';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = jsonDecode(response.body)['message'] ??
              'Failed to update profile';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error updating profile: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> deleteUser() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final response = await http.delete(
        Uri.parse('http://localhost:3000/api/users/${widget.userId}'),
      );

      if (response.statusCode == 200) {
        setState(() {
          _successMessage = 'Account deleted successfully';
          _isLoading = false;
          Navigator.pop(context);
        });
      } else {
        setState(() {
          _errorMessage = jsonDecode(response.body)['message'] ??
              'Failed to delete account';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error deleting account: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 600;
    final double paddingValue = isLargeScreen ? 24.0 : 16.0;
    final double fontSize = isLargeScreen ? 18.0 : 16.0;
    final double iconSize = isLargeScreen ? 60.0 : 50.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Profile'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(paddingValue),
            child: _isLoading
                ? const Center(
                    child:
                        CircularProgressIndicator(color: AppTheme.primaryColor))
                : Form(
                    key: _formKey,
                    child: ListView(
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        Center(
                          child: GestureDetector(
                            onTap: _pickImage,
                            child: CircleAvatar(
                              radius: iconSize,
                              backgroundImage: _profileImage != null
                                  ? FileImage(_profileImage!)
                                  : null,
                              child: _profileImage == null
                                  ? Icon(Icons.person,
                                      size: iconSize,
                                      color: AppTheme.primaryColor)
                                  : null,
                            ),
                          ),
                        ),
                        SizedBox(height: paddingValue / 2),
                        Center(
                          child: TextButton(
                            onPressed: _pickImage,
                            child: Text(
                              'Change Profile Picture',
                              style: TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontSize: fontSize),
                            ),
                          ),
                        ),
                        SizedBox(height: paddingValue),
                        TextFormField(
                          initialValue: username,
                          decoration: const InputDecoration(
                            labelText: 'Username',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.person,
                                color: AppTheme.primaryColor),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          onChanged: (value) =>
                              setState(() => username = value),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a username';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: paddingValue),
                        TextFormField(
                          initialValue: email,
                          decoration: const InputDecoration(
                            labelText: 'Email',
                            border: OutlineInputBorder(),
                            prefixIcon:
                                Icon(Icons.email, color: AppTheme.primaryColor),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          onChanged: (value) => setState(() => email = value),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter an email';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                .hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: paddingValue),
                        DropdownButtonFormField<String>(
                          value: role.isNotEmpty ? role : null,
                          decoration: const InputDecoration(
                            labelText: 'Role',
                            border: OutlineInputBorder(),
                            prefixIcon:
                                Icon(Icons.work, color: AppTheme.primaryColor),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          items: ['user', 'admin'].map((String role) {
                            return DropdownMenuItem<String>(
                              value: role,
                              child: Text(role,
                                  style: TextStyle(fontSize: fontSize)),
                            );
                          }).toList(),
                          onChanged: (value) => setState(() => role = value!),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a role';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: paddingValue),
                        if (_errorMessage != null)
                          Text(
                            _errorMessage!,
                            style: TextStyle(
                                color: Colors.red, fontSize: fontSize),
                            textAlign: TextAlign.center,
                          ),
                        if (_successMessage != null)
                          Text(
                            _successMessage!,
                            style: TextStyle(
                                color: Colors.green, fontSize: fontSize),
                            textAlign: TextAlign.center,
                          ),
                        SizedBox(height: paddingValue),
                        ElevatedButton(
                          onPressed: _isLoading ? null : updateUser,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            padding:
                                EdgeInsets.symmetric(vertical: paddingValue),
                          ),
                          child: _isLoading
                              ? const CircularProgressIndicator(
                                  color: Colors.white)
                              : Text(
                                  'Update Profile',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: fontSize),
                                ),
                        ),
                        SizedBox(height: paddingValue),
                        OutlinedButton(
                          onPressed: _isLoading
                              ? null
                              : () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('Delete Account'),
                                      content: const Text(
                                          'Are you sure you want to delete your account? This action cannot be undone.'),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('Cancel'),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            deleteUser();
                                          },
                                          child: const Text(
                                            'Delete',
                                            style: TextStyle(color: Colors.red),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.red),
                            padding:
                                EdgeInsets.symmetric(vertical: paddingValue),
                          ),
                          child: Text(
                            'Delete Account',
                            style: TextStyle(
                                color: Colors.red, fontSize: fontSize),
                          ),
                        ),
                        SizedBox(height: paddingValue),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}

class ChangePasswordScreen extends StatefulWidget {
  final String userId;
  const ChangePasswordScreen({super.key, required this.userId});

  @override
  _ChangePasswordScreenState createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  String id = '';
  String oldPassword = '';
  String newPassword = '';
  String confirmPassword = '';
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  Future<void> changePassword() async {
    if (!_formKey.currentState!.validate()) return;
    if (oldPassword.isEmpty || newPassword.isEmpty) {
      setState(() {
        _errorMessage = 'Please provide both old and new passwords';
      });
      return;
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    id = prefs.getString('userId') ?? '0';
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final response = await http.put(
        Uri.parse('http://localhost:3000/api/users/change-password'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'id': id,
          'oldPassword': oldPassword,
          'newPassword': newPassword,
        }),
      );

      if (response.statusCode == 200) {
        setState(() {
          _successMessage = 'Password updated successfully';
          oldPassword = '';
          newPassword = '';
          confirmPassword = '';
          _isLoading = false;
        });
      } else {
        final errorMsg =
            jsonDecode(response.body)['message'] ?? 'Failed to update password';
        setState(() {
          _errorMessage = errorMsg;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error updating password: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 600;
    final double paddingValue = isLargeScreen ? 24.0 : 16.0;
    final double fontSize = isLargeScreen ? 18.0 : 16.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Change Password'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromRGBO(255, 226, 159, 1),
              Color(0xFFFFC0CB),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(paddingValue),
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Colors.purple))
                : Form(
                    key: _formKey,
                    child: ListView(
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'Old Password',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.lock, color: Colors.purple),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          obscureText: true,
                          onChanged: (value) =>
                              setState(() => oldPassword = value),
                        ),
                        SizedBox(height: paddingValue),
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'New Password',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.lock, color: Colors.purple),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          obscureText: true,
                          onChanged: (value) =>
                              setState(() => newPassword = value),
                          validator: (value) {
                            if (value != null &&
                                value.isNotEmpty &&
                                value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: paddingValue),
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'Confirm New Password',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.lock, color: Colors.purple),
                          ),
                          style: TextStyle(fontSize: fontSize),
                          obscureText: true,
                          onChanged: (value) =>
                              setState(() => confirmPassword = value),
                          validator: (value) {
                            if (value != null &&
                                value.isNotEmpty &&
                                value != newPassword) {
                              return 'Passwords do not match';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: paddingValue),
                        if (_errorMessage != null)
                          Text(
                            _errorMessage!,
                            style: TextStyle(
                                color: Colors.red, fontSize: fontSize),
                            textAlign: TextAlign.center,
                          ),
                        if (_successMessage != null)
                          Text(
                            _successMessage!,
                            style: TextStyle(
                                color: Colors.green, fontSize: fontSize),
                            textAlign: TextAlign.center,
                          ),
                        SizedBox(height: paddingValue),
                        ElevatedButton(
                          onPressed: _isLoading ? null : changePassword,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            padding:
                                EdgeInsets.symmetric(vertical: paddingValue),
                          ),
                          child: Text(
                            'Change Password',
                            style: TextStyle(
                                color: Colors.white, fontSize: fontSize),
                          ),
                        ),
                        SizedBox(height: paddingValue),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}

class SupportScreen extends StatelessWidget {
  const SupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 600;
    final double titleFontSize = isLargeScreen ? 28 : 24;
    final double bodyFontSize = isLargeScreen ? 18 : 16;
    final double iconSize = isLargeScreen ? 32 : 24;
    final double paddingValue = isLargeScreen ? 24.0 : 16.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Support'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromRGBO(255, 226, 159, 1),
              Color(0xFFFFC0CB),
            ],
          ),
        ),
        child: SafeArea(
          child: ListView(
            shrinkWrap: true,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(paddingValue),
            children: [
              Text(
                'Contact Support',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
              SizedBox(height: paddingValue),
              Text(
                'For any issues or inquiries, please reach out to our support team via email:',
                style: TextStyle(fontSize: bodyFontSize, color: Colors.black87),
              ),
              SizedBox(height: paddingValue),
              ListTile(
                leading:
                    Icon(Icons.email, color: Colors.purple, size: iconSize),
                title: Text(
                  '<EMAIL>',
                  style: TextStyle(fontSize: bodyFontSize),
                ),
                onTap: () {
                  // Optionally add email client launch functionality
                },
              ),
              ListTile(
                leading:
                    Icon(Icons.email, color: Colors.purple, size: iconSize),
                title: Text(
                  '<EMAIL>',
                  style: TextStyle(fontSize: bodyFontSize),
                ),
                onTap: () {
                  // Optionally add email client launch functionality
                },
              ),
              SizedBox(height: paddingValue * 2),
              Text(
                'We are here to help you with any questions or concerns. Our team typically responds within 24-48 hours.',
                style: TextStyle(fontSize: bodyFontSize, color: Colors.black87),
              ),
              SizedBox(height: paddingValue * 2),
            ],
          ),
        ),
      ),
    );
  }
}

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 600;
    final double titleFontSize = isLargeScreen ? 28 : 24;
    final double bodyFontSize = isLargeScreen ? 18 : 16;
    final double paddingValue = isLargeScreen ? 24.0 : 16.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('About MindEase'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromRGBO(255, 226, 159, 1),
              Color(0xFFFFC0CB),
            ],
          ),
        ),
        child: SafeArea(
          child: ListView(
            shrinkWrap: true,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(paddingValue),
            children: [
              Text(
                'About MindEase',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
              SizedBox(height: paddingValue),
              Text(
                'MindEase is a mental health platform designed to make professional support accessible and convenient. Our mission is to empower individuals to prioritize their mental well-being by connecting them with licensed therapists and providing tools to manage their mental health journey.',
                style: TextStyle(fontSize: bodyFontSize, color: Colors.black87),
              ),
              SizedBox(height: paddingValue),
              Text(
                'Key Features:',
                style: TextStyle(
                  fontSize: bodyFontSize + 2,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
              SizedBox(height: paddingValue / 2),
              Text(
                '• Therapist Directory: Browse and search for licensed therapists by name, with detailed profiles to help you find the right match.\n'
                '• User Profiles: Personalize your experience by managing your profile, including username, email, and profile picture.\n'
                '• Responsive Design: Enjoy a seamless experience across devices, with intuitive navigation to access therapist details and settings.\n'
                '• Support Access: Reach out to our dedicated support team for assistance whenever needed.',
                style: TextStyle(fontSize: bodyFontSize, color: Colors.black87),
              ),
              SizedBox(height: paddingValue),
              Text(
                'At MindEase, we believe mental health matters. Join us in creating a world where everyone has the support they need to thrive.',
                style: TextStyle(fontSize: bodyFontSize, color: Colors.black87),
              ),
              SizedBox(height: paddingValue),
            ],
          ),
        ),
      ),
    );
  }
}

class PaymentManagementScreen extends StatefulWidget {
  final String userId;
  const PaymentManagementScreen({super.key, required this.userId});

  @override
  _PaymentManagementScreenState createState() =>
      _PaymentManagementScreenState();
}

class _PaymentManagementScreenState extends State<PaymentManagementScreen> {
  bool _isLoading = false;
  List<dynamic> _paymentHistory = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchPaymentHistory();
  }

  Future<void> _fetchPaymentHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/payments/user/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _paymentHistory = data is List ? data : [];
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load payment history';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading payment history: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addPaymentMethod() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Create a setup intent for adding payment method
      final setupIntentData = await StripeService.createSetupIntent();

      if (setupIntentData != null) {
        final success = await StripeService.setupPaymentMethod(
          clientSecret: setupIntentData['clientSecret'],
        );

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Payment method added successfully!')),
          );
          _fetchPaymentHistory(); // Refresh the list
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to add payment method')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Methods'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: _isLoading
              ? const Center(
                  child:
                      CircularProgressIndicator(color: AppTheme.primaryColor),
                )
              : Column(
                  children: [
                    // Add Payment Method Button
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.spacingM),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.cardGradient,
                          borderRadius: BorderRadius.circular(AppTheme.radiusM),
                          boxShadow: AppTheme.softShadow,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _addPaymentMethod,
                            borderRadius:
                                BorderRadius.circular(AppTheme.radiusM),
                            child: Padding(
                              padding: const EdgeInsets.all(AppTheme.spacingM),
                              child: Row(
                                children: [
                                  Container(
                                    padding:
                                        const EdgeInsets.all(AppTheme.spacingS),
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryColor
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(
                                          AppTheme.radiusS),
                                    ),
                                    child: const Icon(
                                      Icons.add_card,
                                      color: AppTheme.primaryColor,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: AppTheme.spacingM),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Add Payment Method',
                                          style: AppTheme.bodyLarge.copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: AppTheme.textPrimary,
                                          ),
                                        ),
                                        const SizedBox(
                                            height: AppTheme.spacingXS),
                                        Text(
                                          'Add a new credit or debit card',
                                          style: AppTheme.bodyMedium.copyWith(
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Icon(
                                    Icons.arrow_forward_ios,
                                    color: AppTheme.textLight,
                                    size: 16,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Payment History
                    Expanded(
                      child: _errorMessage != null
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red.withOpacity(0.5),
                                  ),
                                  const SizedBox(height: AppTheme.spacingM),
                                  Text(
                                    _errorMessage!,
                                    style: AppTheme.bodyLarge.copyWith(
                                      color: Colors.red,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: AppTheme.spacingM),
                                  ElevatedButton(
                                    onPressed: _fetchPaymentHistory,
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            )
                          : _paymentHistory.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.payment,
                                        size: 64,
                                        color:
                                            AppTheme.textLight.withOpacity(0.5),
                                      ),
                                      const SizedBox(height: AppTheme.spacingM),
                                      Text(
                                        'No payment history',
                                        style: AppTheme.bodyLarge.copyWith(
                                          color: AppTheme.textSecondary,
                                        ),
                                      ),
                                      const SizedBox(height: AppTheme.spacingS),
                                      Text(
                                        'Your payment transactions will appear here',
                                        style: AppTheme.bodyMedium.copyWith(
                                          color: AppTheme.textLight,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  padding:
                                      const EdgeInsets.all(AppTheme.spacingM),
                                  itemCount: _paymentHistory.length,
                                  itemBuilder: (context, index) {
                                    final payment = _paymentHistory[index];
                                    return Container(
                                      margin: const EdgeInsets.only(
                                          bottom: AppTheme.spacingM),
                                      decoration: BoxDecoration(
                                        gradient: AppTheme.cardGradient,
                                        borderRadius: BorderRadius.circular(
                                            AppTheme.radiusM),
                                        boxShadow: AppTheme.softShadow,
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(
                                            AppTheme.spacingM),
                                        child: Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(
                                                  AppTheme.spacingS),
                                              decoration: BoxDecoration(
                                                color: _getStatusColor(payment[
                                                        'transactionStatus'])
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        AppTheme.radiusS),
                                              ),
                                              child: Icon(
                                                _getStatusIcon(payment[
                                                    'transactionStatus']),
                                                color: _getStatusColor(payment[
                                                    'transactionStatus']),
                                                size: 20,
                                              ),
                                            ),
                                            const SizedBox(
                                                width: AppTheme.spacingM),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    '\$${payment['amount']?.toStringAsFixed(2) ?? '0.00'}',
                                                    style: AppTheme.bodyLarge
                                                        .copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color:
                                                          AppTheme.textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                      height:
                                                          AppTheme.spacingXS),
                                                  Text(
                                                    payment['transactionStatus'] ??
                                                        'Unknown',
                                                    style: AppTheme.bodyMedium
                                                        .copyWith(
                                                      color: _getStatusColor(
                                                          payment[
                                                              'transactionStatus']),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                      height:
                                                          AppTheme.spacingXS),
                                                  Text(
                                                    _formatDate(
                                                        payment['paymentDate']),
                                                    style: AppTheme.bodySmall
                                                        .copyWith(
                                                      color: AppTheme.textLight,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'pending':
        return Icons.access_time;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help_outline;
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Unknown date';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Invalid date';
    }
  }
}
