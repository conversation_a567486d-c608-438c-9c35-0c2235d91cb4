const axios = require('axios');

async function testTherapistCreation() {
  try {
    console.log('Testing therapist creation...');
    
    const therapistData = {
      name: 'Dr. Test Therapist',
      specialty: 'Anxiety & Depression',
      location: 'Karachi, Pakistan',
      email: `test.therapist.${Date.now()}@example.com`,
      password: 'testpass123',
      phone: '+92-300-1234567',
      experience: 5,
      coordinates: {
        type: 'Point',
        coordinates: [67.0376941, 24.8779919] // Karachi coordinates
      },
      hourlyRate: 150,
      bio: 'Experienced therapist specializing in anxiety and depression with 5 years of practice.'
    };
    
    console.log('Sending request to create therapist...');
    console.log('Data:', JSON.stringify(therapistData, null, 2));
    
    const response = await axios.post('http://localhost:3000/api/therapists', therapistData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ SUCCESS: Therapist created successfully!');
    console.log('Response status:', response.status);
    console.log('Therapist ID:', response.data._id);
    console.log('Therapist name:', response.data.name);
    console.log('Approval status:', response.data.isApproved);
    console.log('Blocked status:', response.data.isBlocked);
    
  } catch (error) {
    console.error('❌ ERROR: Failed to create therapist');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else {
      console.error('Error message:', error.message);
    }
  }
}

testTherapistCreation();
