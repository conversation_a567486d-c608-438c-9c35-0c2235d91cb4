const http = require('http');
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testNetworkConnectivity() {
  console.log('🌐 Testing Network Connectivity for Admin Portal...\n');

  // Test if server is reachable
  console.log('1. Testing server connectivity...');
  try {
    const response = await axios.get(`${BASE_URL}/users`, {
      timeout: 5000,
      headers: {
        'User-Agent': 'Flutter-Admin-Portal',
        'Content-Type': 'application/json'
      }
    });
    console.log(`✅ Server is reachable: ${response.status}`);
    console.log(`📊 Response size: ${JSON.stringify(response.data).length} bytes`);
  } catch (error) {
    console.error('❌ Server connectivity failed:', error.message);
    return;
  }

  // Test CORS headers
  console.log('\n2. Testing CORS headers...');
  try {
    const response = await axios.options(`${BASE_URL}/users`);
    console.log(`✅ OPTIONS request successful: ${response.status}`);
    console.log('CORS Headers:');
    Object.keys(response.headers).forEach(key => {
      if (key.toLowerCase().includes('cors') || key.toLowerCase().includes('access-control')) {
        console.log(`   ${key}: ${response.headers[key]}`);
      }
    });
  } catch (error) {
    console.log('⚠️ OPTIONS request failed (might be normal):', error.message);
  }

  // Test all admin endpoints
  console.log('\n3. Testing all admin endpoints...');
  const endpoints = [
    { name: 'Users', url: `${BASE_URL}/users` },
    { name: 'Therapists', url: `${BASE_URL}/therapists` },
    { name: 'Appointments', url: `${BASE_URL}/appointments` }
  ];

  for (const endpoint of endpoints) {
    try {
      const start = Date.now();
      const response = await axios.get(endpoint.url, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      const duration = Date.now() - start;
      
      console.log(`✅ ${endpoint.name}: ${response.status} (${duration}ms)`);
      console.log(`   Records: ${Array.isArray(response.data) ? response.data.length : 'Not an array'}`);
      
      if (Array.isArray(response.data) && response.data.length > 0) {
        const sample = response.data[0];
        console.log(`   Sample keys: ${Object.keys(sample).slice(0, 5).join(', ')}`);
      }
    } catch (error) {
      console.error(`❌ ${endpoint.name} failed:`, error.message);
      if (error.response) {
        console.error(`   Status: ${error.response.status}`);
        console.error(`   Data: ${error.response.data}`);
      }
    }
  }

  // Test server health
  console.log('\n4. Testing server health...');
  try {
    const response = await axios.get('http://localhost:3000', { timeout: 5000 });
    console.log(`✅ Server root accessible: ${response.status}`);
  } catch (error) {
    console.log('⚠️ Server root not accessible:', error.message);
  }

  console.log('\n🎯 Network connectivity test completed!');
}

testNetworkConnectivity();
