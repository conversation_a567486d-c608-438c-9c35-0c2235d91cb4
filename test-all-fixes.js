console.log('🧪 Testing all fixes for Mindease project...\n');

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_ID = '680aad8bdef6a277563a942c'; // Replace with actual user ID

async function testMoodJournalFix() {
  console.log('1. 🧠 Testing Mood Journal API Fix...');
  
  try {
    // Test mood entry creation
    const moodResponse = await axios.post(`${BASE_URL}/mood`, {
      userId: TEST_USER_ID,
      mood: 'happy',
      note: 'Testing mood journal fix',
      intensity: 8
    });
    
    console.log('✅ Mood entry created:', moodResponse.data._id);
    
    // Test mood fetching with correct endpoint
    const fetchResponse = await axios.get(`${BASE_URL}/mood/${TEST_USER_ID}`);
    console.log('✅ Mood entries fetched:', fetchResponse.data.length, 'entries');
    
    if (fetchResponse.data.length > 0) {
      console.log('   Latest mood:', fetchResponse.data[0].mood);
    }
    
  } catch (error) {
    console.error('❌ Mood journal test failed:', error.response?.data || error.message);
  }
}

async function testNearbyTherapistsAPI() {
  console.log('\n2. 📍 Testing Nearby Therapists API...');
  
  try {
    // Test with San Francisco coordinates
    const nearbyResponse = await axios.get(`${BASE_URL}/therapists/nearby`, {
      params: {
        lat: 37.7749,
        lng: -122.4194,
        radius: 50
      }
    });
    
    console.log('✅ Nearby therapists API working');
    console.log('   Search center:', nearbyResponse.data.searchCenter);
    console.log('   Radius:', nearbyResponse.data.radiusKm, 'km');
    console.log('   Found therapists:', nearbyResponse.data.count);
    
    // Test error handling
    const errorResponse = await axios.get(`${BASE_URL}/therapists/nearby`).catch(err => err.response);
    if (errorResponse.status === 400) {
      console.log('✅ Error handling works for missing coordinates');
    }
    
  } catch (error) {
    console.error('❌ Nearby therapists test failed:', error.response?.data || error.message);
  }
}

async function testTherapistRegistration() {
  console.log('\n3. 👩‍⚕️ Testing Enhanced Therapist Registration...');
  
  try {
    const therapistData = {
      name: 'Dr. Test Therapist',
      specialty: 'Anxiety & Stress Management',
      location: 'San Francisco, CA',
      email: `test.therapist.${Date.now()}@example.com`,
      password: 'testpass123',
      phone: '******-0123',
      experience: 5,
      coordinates: {
        type: 'Point',
        coordinates: [-122.4194, 37.7749] // San Francisco coordinates
      },
      hourlyRate: 150,
      bio: 'Experienced therapist specializing in anxiety and stress management with 5 years of practice.'
    };
    
    const response = await axios.post(`${BASE_URL}/therapists`, therapistData);
    console.log('✅ Therapist registration with geolocation successful');
    console.log('   Therapist ID:', response.data._id);
    console.log('   Coordinates:', response.data.coordinates.coordinates);
    
    // Test if this therapist shows up in nearby search
    const nearbyResponse = await axios.get(`${BASE_URL}/therapists/nearby`, {
      params: {
        lat: 37.7749,
        lng: -122.4194,
        radius: 10
      }
    });
    
    console.log('   Therapist appears in nearby search:', nearbyResponse.data.count > 0 ? 'Yes' : 'No');
    
  } catch (error) {
    console.error('❌ Therapist registration test failed:', error.response?.data || error.message);
  }
}

async function testPaymentEndpoints() {
  console.log('\n4. 💳 Testing Payment Management Endpoints...');
  
  try {
    // Test payment history endpoint
    const paymentHistoryResponse = await axios.get(`${BASE_URL}/payments/user/${TEST_USER_ID}`);
    console.log('✅ Payment history endpoint working');
    console.log('   Payment records:', paymentHistoryResponse.data.length || 0);
    
    // Test Stripe setup intent endpoint
    const setupIntentResponse = await axios.post(`${BASE_URL}/stripe/create-setup-intent`, {
      customer_id: 'test_customer'
    });
    console.log('✅ Stripe setup intent endpoint working');
    console.log('   Setup intent created:', !!setupIntentResponse.data.clientSecret);
    
  } catch (error) {
    console.error('❌ Payment endpoints test failed:', error.response?.data || error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting comprehensive test suite...\n');
  
  await testMoodJournalFix();
  await testNearbyTherapistsAPI();
  await testTherapistRegistration();
  await testPaymentEndpoints();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📋 Summary of fixes implemented:');
  console.log('   ✅ Fixed mood journal API endpoint mismatch');
  console.log('   ✅ Added nearby therapists geolocation API');
  console.log('   ✅ Enhanced therapist registration with proper coordinates');
  console.log('   ✅ Added payment management screen to settings');
  console.log('   ✅ Added Stripe setup intent for payment methods');
}

// Run the tests
runAllTests().catch(console.error);
