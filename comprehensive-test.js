console.log('🔍 COMPREHENSIVE MINDEASE FEATURE TEST - A to Z\n');

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, success, details = '') {
  const status = success ? '✅' : '❌';
  console.log(`${status} ${name}${details ? ': ' + details : ''}`);
  testResults.tests.push({ name, success, details });
  if (success) testResults.passed++;
  else testResults.failed++;
}

async function testAuthenticationSystem() {
  console.log('\n🔐 AUTHENTICATION SYSTEM');
  
  try {
    // Test user registration
    const userEmail = `testuser${Date.now()}@example.com`;
    const userResponse = await axios.post(`${BASE_URL}/users/signup`, {
      username: `testuser${Date.now()}`,
      email: userEmail,
      password: 'testpass123',
      role: 'user'
    });
    logTest('User Registration', true, `User ID: ${userResponse.data._id}`);
    
    // Test user login
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: userEmail,
      password: 'testpass123'
    });
    logTest('User Login', true, 'Login successful');
    
    return userResponse.data._id;
  } catch (error) {
    logTest('Authentication System', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testTherapistSystem() {
  console.log('\n👩‍⚕️ THERAPIST MANAGEMENT SYSTEM');
  
  try {
    // Test therapist registration with geolocation
    const therapistEmail = `therapist${Date.now()}@example.com`;
    const therapistData = {
      name: 'Dr. Test Therapist',
      specialty: 'Anxiety & Depression',
      location: 'San Francisco, CA',
      email: therapistEmail,
      password: 'therapist123',
      phone: '******-0123',
      experience: 5,
      coordinates: {
        type: 'Point',
        coordinates: [-122.4194, 37.7749] // San Francisco
      },
      hourlyRate: 150,
      bio: 'Experienced therapist specializing in anxiety and depression.'
    };
    
    const therapistResponse = await axios.post(`${BASE_URL}/therapists`, therapistData);
    logTest('Therapist Registration with Geolocation', true, `Therapist ID: ${therapistResponse.data._id}`);
    
    // First approve the therapist
    const approveResponse = await axios.put(`${BASE_URL}/therapists/${therapistResponse.data._id}/approve`, {
      approved: true
    });
    logTest('Therapist Approval', true, 'Therapist approved by admin');

    // Test therapist login
    const therapistLogin = await axios.post(`${BASE_URL}/therapists/login`, {
      email: therapistEmail,
      password: 'therapist123',
      role: 'therapist'
    });
    logTest('Therapist Login', true, 'Login successful');
    
    // Test get all therapists
    const allTherapists = await axios.get(`${BASE_URL}/therapists`);
    logTest('Get All Therapists', true, `Found ${allTherapists.data.length} therapists`);
    
    // Test nearby therapists API
    const nearbyTherapists = await axios.get(`${BASE_URL}/therapists/nearby`, {
      params: { lat: 37.7749, lng: -122.4194, radius: 50 }
    });
    logTest('Nearby Therapists API', true, `Search radius: ${nearbyTherapists.data.radiusKm}km`);
    
    return therapistResponse.data._id;
  } catch (error) {
    logTest('Therapist System', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testMoodJournalSystem(userId) {
  console.log('\n🧠 MOOD & JOURNAL SYSTEM');
  
  try {
    // Test mood entry creation
    const moodResponse = await axios.post(`${BASE_URL}/mood`, {
      userId: userId,
      mood: 'happy',
      note: 'Feeling great today!',
      intensity: 8
    });
    logTest('Mood Entry Creation', true, `Mood ID: ${moodResponse.data._id}`);
    
    // Test mood fetching (fixed endpoint)
    const moodHistory = await axios.get(`${BASE_URL}/mood/${userId}`);
    logTest('Mood History Fetching', true, `Found ${moodHistory.data.length} mood entries`);
    
    // Test journal entry creation
    const journalResponse = await axios.post(`${BASE_URL}/journal/create`, {
      userId: userId,
      title: 'Test Journal Entry',
      content: 'This is a test journal entry to verify the system is working.',
      mood: 'content'
    });
    logTest('Journal Entry Creation', true, `Journal ID: ${journalResponse.data._id}`);
    
    // Test journal history
    const journalHistory = await axios.get(`${BASE_URL}/journal/user/${userId}`);
    logTest('Journal History Fetching', true, `Found ${journalHistory.data.length} journal entries`);
    
  } catch (error) {
    logTest('Mood & Journal System', false, error.response?.data?.message || error.message);
  }
}

async function testAppointmentSystem(userId, therapistId) {
  console.log('\n📅 APPOINTMENT SYSTEM');
  
  try {
    // Test appointment booking
    const appointmentData = {
      userId: userId,
      therapistId: therapistId,
      appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      appointmentTime: '14:00',
      appointmentType: 'online',
      duration: 60, // Add required duration field
      notes: 'Test appointment booking'
    };
    
    const appointmentResponse = await axios.post(`${BASE_URL}/appointments`, appointmentData);
    logTest('Appointment Booking', true, `Appointment ID: ${appointmentResponse.data._id}`);
    
    // Test get user appointments
    const userAppointments = await axios.get(`${BASE_URL}/appointments/user/${userId}`);
    logTest('Get User Appointments', true, `Found ${userAppointments.data.length} appointments`);
    
    // Test get therapist appointments
    const therapistAppointments = await axios.get(`${BASE_URL}/appointments/therapist/${therapistId}`);
    logTest('Get Therapist Appointments', true, `Found ${therapistAppointments.data.length} appointments`);
    
    return appointmentResponse.data._id;
  } catch (error) {
    logTest('Appointment System', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testPaymentSystem(userId) {
  console.log('\n💳 PAYMENT SYSTEM');
  
  try {
    // Test payment creation
    const paymentData = {
      userId: userId,
      amount: 150.00,
      paymentDate: new Date().toISOString(),
      transactionStatus: 'Completed'
    };
    
    const paymentResponse = await axios.post(`${BASE_URL}/payments`, paymentData);
    logTest('Payment Creation', true, `Payment ID: ${paymentResponse.data._id}`);
    
    // Test get user payments
    const userPayments = await axios.get(`${BASE_URL}/payments/user/${userId}`);
    logTest('Get User Payment History', true, `Found ${userPayments.data.length} payments`);
    
    // Test Stripe setup intent (optional - may fail if Stripe not configured)
    try {
      const setupIntentResponse = await axios.post(`${BASE_URL}/stripe/create-setup-intent`, {
        customer_id: 'test_customer'
      });
      logTest('Stripe Setup Intent', true, 'Setup intent created successfully');
    } catch (error) {
      logTest('Stripe Setup Intent', false, 'Stripe not configured (optional)');
    }

    // Test Stripe payment intent (optional - may fail if Stripe not configured)
    try {
      const paymentIntentResponse = await axios.post(`${BASE_URL}/stripe/create-payment-intent`, {
        amount: 15000, // $150.00 in cents
        currency: 'usd'
      });
      logTest('Stripe Payment Intent', true, 'Payment intent created successfully');
    } catch (error) {
      logTest('Stripe Payment Intent', false, 'Stripe not configured (optional)');
    }
    
  } catch (error) {
    logTest('Payment System', false, error.response?.data?.message || error.message);
  }
}

async function testAIAnalysisSystem(userId) {
  console.log('\n🤖 AI ANALYSIS SYSTEM');
  
  try {
    // Test AI analysis
    const analysisResponse = await axios.post(`${BASE_URL}/ai-analysis/analyze/${userId}`, {
      content: 'I have been feeling anxious lately and having trouble sleeping.'
    });
    logTest('AI Text Analysis', true, `Analysis completed`);
    
    // Test analysis history
    const historyResponse = await axios.get(`${BASE_URL}/ai-analysis/history/${userId}`);
    logTest('AI Analysis History', true, `Found ${historyResponse.data.length} analyses`);
    
  } catch (error) {
    logTest('AI Analysis System', false, error.response?.data?.message || error.message);
  }
}

async function testLocationServices() {
  console.log('\n📍 LOCATION SERVICES');
  
  try {
    // Test IP-based geolocation
    const ipLocationResponse = await axios.get(`${BASE_URL}/location/ip-geo`);
    logTest('IP-based Geolocation', true, `Location: ${ipLocationResponse.data.city}, ${ipLocationResponse.data.region}`);
    
    // Test location update
    const locationUpdateResponse = await axios.post(`${BASE_URL}/location/update`, {
      userId: 'test_user',
      latitude: 37.7749,
      longitude: -122.4194
    });
    logTest('Location Update', true, 'Location updated successfully');
    
  } catch (error) {
    logTest('Location Services', false, error.response?.data?.message || error.message);
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting comprehensive A-Z feature test...\n');
  
  // Test all systems
  const userId = await testAuthenticationSystem();
  const therapistId = await testTherapistSystem();
  
  if (userId) {
    await testMoodJournalSystem(userId);
    await testPaymentSystem(userId);
    await testAIAnalysisSystem(userId);
    
    if (therapistId) {
      await testAppointmentSystem(userId, therapistId);
    }
  }
  
  await testLocationServices();
  
  // Print summary
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests.filter(t => !t.success).forEach(test => {
      console.log(`   - ${test.name}: ${test.details}`);
    });
  }
  
  console.log('\n🎉 Comprehensive testing completed!');
}

// Run the comprehensive test
runComprehensiveTest().catch(console.error);
