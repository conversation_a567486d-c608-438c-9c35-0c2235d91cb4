const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up MongoDB for Mental Health App...\n');

// Create data directory for MongoDB
const createDataDirectory = () => {
  const dataDir = 'C:\\data\\db';
  if (!fs.existsSync(dataDir)) {
    try {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('✅ Created MongoDB data directory:', dataDir);
    } catch (error) {
      console.log('⚠️ Could not create data directory:', error.message);
    }
  } else {
    console.log('✅ MongoDB data directory already exists');
  }
};

// Check if MongoDB is installed
const checkMongoInstallation = () => {
  return new Promise((resolve) => {
    exec('mongod --version', (error, stdout, stderr) => {
      if (error) {
        console.log('❌ MongoDB is not installed or not in PATH');
        console.log('📋 Please install MongoDB Community Server from:');
        console.log('   https://www.mongodb.com/try/download/community');
        resolve(false);
      } else {
        console.log('✅ MongoDB is installed');
        console.log('📋 Version:', stdout.split('\n')[0]);
        resolve(true);
      }
    });
  });
};

// Start MongoDB service
const startMongoDB = () => {
  return new Promise((resolve) => {
    console.log('🔧 Starting MongoDB service...');
    
    // Try Windows service first
    exec('net start MongoDB', (error, stdout, stderr) => {
      if (!error) {
        console.log('✅ MongoDB Windows service started');
        resolve(true);
        return;
      }
      
      // Try starting mongod directly
      console.log('🔧 Trying to start mongod directly...');
      const mongodProcess = exec('mongod --dbpath "C:\\data\\db"', (error, stdout, stderr) => {
        if (error) {
          console.log('❌ Failed to start mongod:', error.message);
          resolve(false);
        }
      });
      
      // Give it some time to start
      setTimeout(() => {
        console.log('✅ MongoDB started (background process)');
        resolve(true);
      }, 3000);
    });
  });
};

// Test MongoDB connection
const testConnection = () => {
  return new Promise((resolve) => {
    exec('mongo --eval "db.runCommand({ping: 1})"', (error, stdout, stderr) => {
      if (error) {
        console.log('❌ MongoDB connection test failed');
        resolve(false);
      } else {
        console.log('✅ MongoDB connection test successful');
        resolve(true);
      }
    });
  });
};

// Create sample data
const createSampleData = () => {
  console.log('📊 Creating sample data...');
  
  const sampleScript = `
use mentalhealth

// Create admin user
db.users.insertOne({
  username: "admin",
  email: "<EMAIL>",
  passwordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
  role: "admin",
  isBlocked: false,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
})

// Create sample user
db.users.insertOne({
  username: "hadyy",
  email: "<EMAIL>",
  passwordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
  role: "user",
  isBlocked: false,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
})

// Create sample therapist
db.therapists.insertOne({
  name: "Dr. Sarah Johnson",
  email: "<EMAIL>",
  passwordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
  phone: "******-0123",
  specialty: "Anxiety & Depression",
  experience: 8,
  location: "San Francisco, CA",
  rating: 4.8,
  hourlyRate: 120,
  bio: "Specialized in cognitive behavioral therapy with 8 years of experience.",
  isApproved: false,
  isBlocked: false,
  createdAt: new Date(),
  updatedAt: new Date()
})

print("✅ Sample data created successfully!")
`;

  fs.writeFileSync('sample-data.js', sampleScript);
  
  exec('mongo < sample-data.js', (error, stdout, stderr) => {
    if (error) {
      console.log('⚠️ Could not create sample data:', error.message);
    } else {
      console.log('✅ Sample data created successfully');
    }
    
    // Clean up
    try {
      fs.unlinkSync('sample-data.js');
    } catch (e) {}
  });
};

// Main setup function
const setupMongoDB = async () => {
  console.log('🔍 Checking MongoDB installation...');
  const isInstalled = await checkMongoInstallation();
  
  if (!isInstalled) {
    console.log('\n💡 Installation Instructions:');
    console.log('1. Download MongoDB Community Server');
    console.log('2. Install with default settings');
    console.log('3. Run this script again');
    return;
  }
  
  console.log('\n🔧 Setting up MongoDB...');
  createDataDirectory();
  
  console.log('\n🚀 Starting MongoDB...');
  const started = await startMongoDB();
  
  if (started) {
    console.log('\n🧪 Testing connection...');
    setTimeout(async () => {
      const connected = await testConnection();
      if (connected) {
        createSampleData();
        console.log('\n🎉 MongoDB setup completed successfully!');
        console.log('📋 You can now run: npm start');
      }
    }, 2000);
  } else {
    console.log('\n❌ Failed to start MongoDB');
    console.log('💡 Try running as administrator or check MongoDB installation');
  }
};

// Run setup
setupMongoDB().catch(console.error);
