const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAdminAPIs() {
  console.log('🔍 Testing Admin Portal API Endpoints...\n');

  try {
    // Test Users API
    console.log('1. Testing Users API...');
    const usersResponse = await axios.get(`${BASE_URL}/users`);
    console.log(`✅ Users API Status: ${usersResponse.status}`);
    console.log(`📊 Users Count: ${usersResponse.data.length}`);
    
    if (usersResponse.data.length > 0) {
      const sampleUser = usersResponse.data[0];
      console.log(`👤 Sample User: ${sampleUser.username} (${sampleUser.email})`);
      console.log(`🔒 Blocked Status: ${sampleUser.isBlocked || false}`);
    }
    console.log('');

    // Test Therapists API
    console.log('2. Testing Therapists API...');
    const therapistsResponse = await axios.get(`${BASE_URL}/therapists`);
    console.log(`✅ Therapists API Status: ${therapistsResponse.status}`);
    console.log(`📊 Therapists Count: ${therapistsResponse.data.length}`);
    
    if (therapistsResponse.data.length > 0) {
      const sampleTherapist = therapistsResponse.data[0];
      console.log(`👩‍⚕️ Sample Therapist: ${sampleTherapist.name} (${sampleTherapist.email})`);
      console.log(`✅ Approved Status: ${sampleTherapist.isApproved || false}`);
      console.log(`🔒 Blocked Status: ${sampleTherapist.isBlocked || false}`);
    }
    console.log('');

    // Test Appointments API
    console.log('3. Testing Appointments API...');
    const appointmentsResponse = await axios.get(`${BASE_URL}/appointments`);
    console.log(`✅ Appointments API Status: ${appointmentsResponse.status}`);
    console.log(`📊 Appointments Count: ${appointmentsResponse.data.length}`);
    
    if (appointmentsResponse.data.length > 0) {
      const sampleAppointment = appointmentsResponse.data[0];
      console.log(`📅 Sample Appointment: ${sampleAppointment.date} at ${sampleAppointment.time}`);
      console.log(`💰 Fee: $${sampleAppointment.fee || 0}`);
    }
    console.log('');

    // Test response format for admin dashboard
    console.log('4. Testing Data Format for Admin Dashboard...');
    console.log('Users data structure:');
    if (usersResponse.data.length > 0) {
      const user = usersResponse.data[0];
      console.log(`  - Has _id: ${!!user._id}`);
      console.log(`  - Has username: ${!!user.username}`);
      console.log(`  - Has email: ${!!user.email}`);
      console.log(`  - Has isBlocked: ${user.hasOwnProperty('isBlocked')}`);
    }

    console.log('Therapists data structure:');
    if (therapistsResponse.data.length > 0) {
      const therapist = therapistsResponse.data[0];
      console.log(`  - Has _id: ${!!therapist._id}`);
      console.log(`  - Has name: ${!!therapist.name}`);
      console.log(`  - Has email: ${!!therapist.email}`);
      console.log(`  - Has isApproved: ${therapist.hasOwnProperty('isApproved')}`);
      console.log(`  - Has isBlocked: ${therapist.hasOwnProperty('isBlocked')}`);
    }

    console.log('\n🎉 All API endpoints are working correctly!');
    console.log('📝 Summary:');
    console.log(`   - Users: ${usersResponse.data.length} records`);
    console.log(`   - Therapists: ${therapistsResponse.data.length} records`);
    console.log(`   - Appointments: ${appointmentsResponse.data.length} records`);

  } catch (error) {
    console.error('❌ Error testing APIs:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

testAdminAPIs();
