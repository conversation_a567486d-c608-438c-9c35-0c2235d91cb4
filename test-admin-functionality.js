// Test admin functionality with real MongoDB Atlas
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAdminFunctionality() {
  console.log('🧪 Testing Admin Functionality with Real MongoDB Atlas\n');
  
  try {
    // 1. Test getting all users
    console.log('1️⃣ Testing: Get all users');
    const usersResponse = await axios.get(`${BASE_URL}/users`);
    console.log(`✅ Found ${usersResponse.data.length} users`);
    
    const testUser = usersResponse.data.find(user => user.email === '<EMAIL>');
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`📋 Test user ID: ${testUser._id}`);
    console.log(`📋 Current blocked status: ${testUser.isBlocked}`);
    
    // 2. Test blocking user
    console.log('\n2️⃣ Testing: Block user');
    const blockResponse = await axios.put(`${BASE_URL}/users/${testUser._id}/block`, {
      isBlocked: true
    });
    console.log('✅ Block user response:', blockResponse.data.message);
    console.log(`📋 User blocked status: ${blockResponse.data.user.isBlocked}`);
    
    // 3. Test login with blocked user
    console.log('\n3️⃣ Testing: Login with blocked user');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('❌ Login should have failed for blocked user');
    } catch (loginError) {
      if (loginError.response && loginError.response.status === 403) {
        console.log('✅ Login correctly blocked:', loginError.response.data.error);
      } else {
        console.log('❌ Unexpected login error:', loginError.message);
      }
    }
    
    // 4. Test unblocking user
    console.log('\n4️⃣ Testing: Unblock user');
    const unblockResponse = await axios.put(`${BASE_URL}/users/${testUser._id}/block`, {
      isBlocked: false
    });
    console.log('✅ Unblock user response:', unblockResponse.data.message);
    console.log(`📋 User blocked status: ${unblockResponse.data.user.isBlocked}`);
    
    // 5. Test login with unblocked user
    console.log('\n5️⃣ Testing: Login with unblocked user');
    const successLoginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ Login successful after unblocking:', successLoginResponse.data.message);
    
    // 6. Test therapist approval functionality
    console.log('\n6️⃣ Testing: Therapist approval functionality');
    const pendingTherapistsResponse = await axios.get(`${BASE_URL}/therapists/admin/pending`);
    console.log(`✅ Found ${pendingTherapistsResponse.data.count} pending therapists`);
    
    if (pendingTherapistsResponse.data.therapists.length > 0) {
      const therapist = pendingTherapistsResponse.data.therapists[0];
      console.log(`📋 Test therapist: ${therapist.name} (${therapist.email})`);
      
      // Test approving therapist
      console.log('\n7️⃣ Testing: Approve therapist');
      const approveResponse = await axios.put(`${BASE_URL}/therapists/${therapist._id}/approve`, {
        adminId: testUser._id,
        approved: true
      });
      console.log('✅ Therapist approved:', approveResponse.data.message);
      
      // Test therapist login after approval
      console.log('\n8️⃣ Testing: Therapist login after approval');
      const therapistLoginResponse = await axios.post(`${BASE_URL}/therapists/login`, {
        email: therapist.email,
        password: 'therapist123',
        role: 'therapist'
      });
      console.log('✅ Therapist login successful:', therapistLoginResponse.data.message);
      
      // Test rejecting therapist (reset for next test)
      console.log('\n9️⃣ Testing: Reject therapist (reset)');
      const rejectResponse = await axios.put(`${BASE_URL}/therapists/${therapist._id}/approve`, {
        adminId: testUser._id,
        approved: false,
        rejectionReason: 'Test rejection'
      });
      console.log('✅ Therapist rejected:', rejectResponse.data.message);
    }
    
    // 7. Test AI Analysis functionality
    console.log('\n🔟 Testing: AI Analysis functionality');
    const analysisResponse = await axios.post(`${BASE_URL}/ai-analysis/analyze/${testUser._id}`, {
      content: 'I feel really anxious about my upcoming presentation. My heart is racing and I cannot focus on anything.'
    });
    console.log('✅ AI Analysis completed:', analysisResponse.data.message);
    console.log('📊 Analysis results:');
    console.log(`   Mental Health: ${analysisResponse.data.result.analysis.mentalHealthStatus.primary}`);
    console.log(`   Sentiment: ${analysisResponse.data.result.analysis.sentimentScore.label}`);
    console.log(`   Risk Level: ${analysisResponse.data.result.analysis.riskLevel.level}`);
    
    console.log('\n🎉 All admin functionality tests passed!');
    console.log('\n📋 Summary:');
    console.log('✅ User blocking/unblocking works correctly');
    console.log('✅ Blocked user login prevention works');
    console.log('✅ Therapist approval system works');
    console.log('✅ AI analysis functionality works');
    console.log('✅ Real-time MongoDB Atlas connection works');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Error details:', error.response.data);
    }
  }
}

// Run the tests
testAdminFunctionality();
