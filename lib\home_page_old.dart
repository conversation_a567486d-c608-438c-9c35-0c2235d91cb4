import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/therapist_screen.dart';
import 'package:myapp/homepage/All_therapist_screen.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/services.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_card.dart';
import 'package:myapp/components/modern_button.dart';
import 'package:myapp/user_appointments_screen.dart';
import 'package:myapp/homepage/Depression_screen.dart' as depression_screen;
import 'package:myapp/homepage/Anxiety_screen.dart' as anxiety_screen;
import 'package:myapp/homepage/stress_screen.dart' as stress_screen;
import 'package:myapp/homepage/insomia_screen.dart' as insomnia_screen;
import 'package:myapp/ai_analysis_screen.dart';

class HomeScreen extends StatefulWidget {
  final String userId;

  const HomeScreen({super.key, required this.userId});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  Map<String, dynamic>? userDetails;
  List<dynamic>? therapists;
  List<dynamic>? filteredTherapists;
  bool isLoading = true;
  String? errorMessage;
  String? meetLink;
  bool isCreatingMeet = false;
  Map<String, dynamic>? userLocation;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchUserDetails();
    _fetchTherapists();
    _fetchUserLocation();
    _searchController.addListener(_filterTherapists);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterTherapists() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        filteredTherapists = therapists;
      });
    } else {
      setState(() {
        filteredTherapists = therapists?.where((therapist) {
          final name = therapist['name']?.toString().toLowerCase() ?? '';
          final specialty =
              therapist['specialty']?.toString().toLowerCase() ?? '';
          final location =
              therapist['location']?.toString().toLowerCase() ?? '';
          return name.contains(query) ||
              specialty.contains(query) ||
              location.contains(query);
        }).toList();
      });
    }
  }

  Future<void> _fetchUserDetails() async {
    final url = 'http://localhost:3000/api/users/${widget.userId}';
    print('Fetching user details from: $url'); // Debug log
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json"
        }, // Add content type header
      );
      print('Response status: ${response.statusCode}'); // Debug log
      print('Response body: ${response.body}'); // Debug log

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        // Handle both direct user object and nested user object
        final userData = responseData is Map
            ? (responseData['user'] ?? responseData)
            : responseData;

        setState(() {
          userDetails = userData;
          isLoading = false;
          errorMessage = null; // Clear any previous errors
        });
      } else {
        final errorMsg = response.body.isNotEmpty
            ? jsonDecode(response.body)['message'] ??
                'Failed to load user details'
            : 'Failed to load user details';
        setState(() {
          errorMessage = errorMsg;
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching user details: $e'); // Debug log
      setState(() {
        errorMessage = 'An error occurred while connecting to the server';
        isLoading = false;
      });
    }
  }

  Future<void> _fetchTherapists() async {
    const url = 'http://localhost:3000/api/therapists';
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        setState(() {
          therapists = jsonDecode(response.body);
          filteredTherapists = therapists; // Initialize filtered list
        });
      } else {
        setState(() {
          errorMessage = 'Failed to load therapists';
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'An error occurred while fetching therapists: $e';
      });
    }
  }

  Future<void> _searchByLocationName(String placeName) async {
    final query = Uri.encodeComponent(placeName);
    final url =
        'https://nominatim.openstreetmap.org/search?q=$query&format=json';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data.isNotEmpty) {
          final lat = double.parse(data[0]['lat']);
          final lon = double.parse(data[0]['lon']);
          print('Coordinates: $lat, $lon');

          await _fetchNearbyTherapistsWithCoordinates(lat, lon);
        } else {
          setState(() {
            errorMessage = 'No results found for "$placeName"';
          });
        }
      } else {
        setState(() {
          errorMessage = 'Failed to geocode location';
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Geocoding error: $e';
      });
    }
  }

  Future<void> _fetchNearbyTherapistsWithCoordinates(
      double lat, double lng) async {
    final url =
        'http://localhost:3000/api/therapists/nearby?lat=$lat&lng=$lng&radius=10';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          therapists = data['therapists'];
        });
      } else {
        setState(() {
          errorMessage = 'Failed to load nearby therapists';
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error fetching therapists: $e';
      });
    }
  }

  Future<void> _fetchUserLocation() async {
    try {
      print('Fetching user location...');

      // Use IP-based location
      await _getIPBasedLocation();
    } catch (e) {
      print('Error fetching location: $e');
      setState(() {
        userLocation = null;
      });
    }
  }

  Future<void> _getIPBasedLocation() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/location/ip-geo'),
      );

      print('IP Location API response status: ${response.statusCode}');
      print('IP Location API response body: ${response.body}');

      if (response.statusCode == 200) {
        final locationData = jsonDecode(response.body);
        locationData['source'] = 'IP';
        setState(() {
          userLocation = locationData;
        });
        print('IP Location data set: $locationData');
      } else {
        print(
            'Failed to fetch IP location: ${response.statusCode} - ${response.body}');
        setState(() {
          userLocation = null;
        });
      }
    } catch (e) {
      print('Error fetching IP location: $e');
      setState(() {
        userLocation = null;
      });
    }
  }

  void _findNearbyTherapists() async {
    if (userLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('Location not available. Please enable location services.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Finding nearby therapists...')),
      );

      // Fetch therapists from API
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/therapists'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> allTherapists = jsonDecode(response.body);

        // Filter therapists by location based on city/region
        List<dynamic> nearbyTherapists = allTherapists.where((therapist) {
          final therapistLocation =
              therapist['location']?.toString().toLowerCase() ?? '';
          final userCity =
              userLocation!['city']?.toString().toLowerCase() ?? '';
          final userRegion =
              userLocation!['region']?.toString().toLowerCase() ?? '';

          return therapistLocation.contains(userCity) ||
              therapistLocation.contains(userRegion) ||
              userCity.contains(therapistLocation) ||
              userRegion.contains(therapistLocation);
        }).toList();

        // If no nearby therapists found, show all therapists
        if (nearbyTherapists.isEmpty) {
          nearbyTherapists = allTherapists;
        }

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AllTherapistsScreen(
                therapists: nearbyTherapists,
                userId: widget.userId,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to load therapists. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<dynamic> _filterTherapistsByDistance(
      List<dynamic> therapists, double userLat, double userLon) {
    const double maxDistanceKm = 50.0; // 50km radius

    List<Map<String, dynamic>> therapistsWithDistance = [];

    for (var therapist in therapists) {
      // For demo purposes, assign random coordinates near user location
      // In a real app, therapists would have actual coordinates in the database
      double therapistLat =
          userLat + (Random().nextDouble() - 0.5) * 0.5; // ±0.25 degrees
      double therapistLon = userLon + (Random().nextDouble() - 0.5) * 0.5;

      double distance =
          _calculateDistance(userLat, userLon, therapistLat, therapistLon);

      if (distance <= maxDistanceKm) {
        Map<String, dynamic> therapistWithDistance =
            Map<String, dynamic>.from(therapist);
        therapistWithDistance['distance'] = distance;
        therapistWithDistance['latitude'] = therapistLat;
        therapistWithDistance['longitude'] = therapistLon;
        therapistsWithDistance.add(therapistWithDistance);
      }
    }

    // Sort by distance
    therapistsWithDistance
        .sort((a, b) => a['distance'].compareTo(b['distance']));

    return therapistsWithDistance;
  }

  // Simple distance calculation using Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  void _navigateToTherapistList() async {
    try {
      // Fetch therapists from API
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/therapists'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> therapists = jsonDecode(response.body);

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AllTherapistsScreen(
                therapists: therapists,
                userId: widget.userId,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to load therapists. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createGoogleMeet() async {
    setState(() {
      isCreatingMeet = true;
    });

    try {
      final requestBody = jsonEncode({
        'userId': widget.userId,
      });

      print(
          'Making API call to: http://localhost:3000/api/google-meet/create-meet');
      print('Request body: $requestBody');

      final response = await http.post(
        Uri.parse('http://localhost:3000/api/google-meet/create-meet'),
        headers: {'Content-Type': 'application/json'},
        body: requestBody,
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Backend response: $data'); // Debug: see what backend returns
        final meetLink =
            data['meetLink'] ?? data['link'] ?? data['meetingLink'] ?? '';
        print('Extracted meet link: $meetLink'); // Debug: see extracted link

        if (meetLink.isNotEmpty) {
          _showMeetLinkDialog(meetLink);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to generate meeting link. Response: ${response.body}')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('Error: ${response.statusCode} - ${response.body}')),
        );
      }
    } catch (e) {
      print('Exception occurred: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        isCreatingMeet = false;
      });
    }
  }

  void _showMeetLinkDialog(String meetLink) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Google Meet Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your meeting link:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: SelectableText(
                meetLink,
                style: const TextStyle(fontSize: 12),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Share this link with your therapist to start the meeting.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: meetLink));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Link copied to clipboard!')),
              );
            },
            child: const Text('Copy Link'),
          ),
          ElevatedButton(
            onPressed: () {
              // You can add logic to open the link in browser or app
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening meeting...')),
              );
              Navigator.of(context).pop();
            },
            child: const Text('Join Meeting'),
          ),
        ],
      ),
    );
  }

  // Helper method for header icons
  Widget _buildHeaderIcon(IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  // Helper method for quick stats
  Widget _buildQuickStat(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _logout() {
    Navigator.pushReplacementNamed(context, '/login');
  }

  void _showLocationInfo() {
    if (userLocation != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Your Location'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('City: ${userLocation!['city'] ?? 'Unknown'}'),
              Text('Region: ${userLocation!['region'] ?? 'Unknown'}'),
              Text('Country: ${userLocation!['country'] ?? 'Unknown'}'),
              const SizedBox(height: 8),
              Text(
                'Source: ${userLocation!['source'] == 'GPS' ? 'GPS Location' : 'IP Location'}',
                style: TextStyle(
                  fontSize: 12,
                  color: userLocation!['source'] == 'GPS'
                      ? Colors.green
                      : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (userLocation!['latitude'] != null &&
                  userLocation!['longitude'] != null)
                Text(
                  'Coordinates: ${userLocation!['latitude']?.toStringAsFixed(4)}, ${userLocation!['longitude']?.toStringAsFixed(4)}',
                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                ),
              const SizedBox(height: 16),
              const Text(
                'We use this to find therapists near you.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _findNearbyTherapists();
              },
              child: const Text('Find Nearby'),
            ),
          ],
        ),
      );
    } else {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Location Not Available'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Unable to determine your location.'),
              SizedBox(height: 8),
              Text(
                'This might be because:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• You\'re testing on localhost'),
              Text('• Network connectivity issues'),
              Text('• Location service is down'),
              SizedBox(height: 16),
              Text(
                'Check the console for detailed error messages.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _fetchUserLocation(); // Retry
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Retrying location fetch...')),
                );
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        slivers: [
          // Modern App Bar with Gradient
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            automaticallyImplyLeading: false,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Welcome back! 👋',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.userId.isNotEmpty ? 'User' : 'Guest',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            // Profile and Location Icons
                            Row(
                              children: [
                                _buildHeaderIcon(
                                  Icons.location_on,
                                  () => _showLocationInfo(),
                                ),
                                const SizedBox(width: 12),
                                _buildHeaderIcon(
                                  Icons.account_circle,
                                  () =>
                                      Navigator.pushNamed(context, '/settings'),
                                ),
                                const SizedBox(width: 12),
                                _buildHeaderIcon(
                                  Icons.logout,
                                  () => _logout(),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Spacer(),
                        // Quick Stats Row
                        Row(
                          children: [
                            _buildQuickStat('Mood Today', '😊', Colors.orange),
                            const SizedBox(width: 20),
                            _buildQuickStat('Sessions', '12', Colors.green),
                            const SizedBox(width: 20),
                            _buildQuickStat('Streak', '7 days', Colors.blue),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Main Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick Actions Section
                  _buildQuickActionsSection(),
                  const SizedBox(height: 24),
                  // Mental Health Tools Section
                  _buildMentalHealthToolsSection(),
                  const SizedBox(height: 24),
                  // AI Analysis Section
                  _buildAIAnalysisSection(),
                  const SizedBox(height: 24),
                  // Therapist Section
                  _buildTherapistSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Quick Actions Section
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '🚀 Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.psychology,
                title: 'AI Analysis',
                subtitle: 'Analyze your mood',
                color: const Color(0xFF667eea),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        AIAnalysisScreen(userId: widget.userId),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.edit_note,
                title: 'Journal',
                subtitle: 'Write your thoughts',
                color: const Color(0xFF48BB78),
                onTap: () => Navigator.pushNamed(context, '/journaling'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.people,
                title: 'Find Therapist',
                subtitle: 'Connect with experts',
                color: const Color(0xFFED8936),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AllTherapistsScreen(
                        therapists: const [], userId: widget.userId),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.location_on,
                title: 'Nearby',
                subtitle: 'Local therapists',
                color: const Color(0xFF9F7AEA),
                onTap: () => _findNearbyTherapists(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Quick Action Card Widget
  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Mental Health Tools Section
  Widget _buildMentalHealthToolsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '🧠 Mental Health Tools',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 16),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildMentalHealthCard(
                'Depression',
                'Track and manage depression symptoms',
                Icons.sentiment_very_dissatisfied,
                const Color(0xFF667eea),
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const depression_screen.DepressionScreen(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              _buildMentalHealthCard(
                'Anxiety',
                'Manage anxiety and stress levels',
                Icons.psychology_outlined,
                const Color(0xFF48BB78),
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const anxiety_screen.AnxietyScreen(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              _buildMentalHealthCard(
                'Stress',
                'Learn stress management techniques',
                Icons.spa,
                const Color(0xFFED8936),
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const stress_screen.StressScreen(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              _buildMentalHealthCard(
                'Insomnia',
                'Improve your sleep quality',
                Icons.bedtime,
                const Color(0xFF9F7AEA),
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const insomnia_screen.InsomniaScreen(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Mental Health Card Widget
  Widget _buildMentalHealthCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 200,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withOpacity(0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // AI Analysis Section
  Widget _buildAIAnalysisSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700).withOpacity(0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  '🤖 AI Powered',
                  style: TextStyle(
                    color: Color(0xFF667eea),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '🧠 AI Journal Analysis',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Get intelligent insights from your journal entries with real-time AI analysis and personalized recommendations',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        AIAnalysisScreen(userId: widget.userId),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF667eea),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 0,
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.analytics, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Start AI Analysis',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Therapist Section
  Widget _buildTherapistSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '👩‍⚕️ Connect with Therapists',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildTherapistActionButton(
                      'Find All Therapists',
                      Icons.search,
                      const Color(0xFF667eea),
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AllTherapistsScreen(
                              therapists: const [], userId: widget.userId),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildTherapistActionButton(
                      'Nearby Therapists',
                      Icons.location_on,
                      const Color(0xFF48BB78),
                      () => _findNearbyTherapists(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildTherapistActionButton(
                'Become a Therapist',
                Icons.person_add,
                const Color(0xFFED8936),
                () => Navigator.pushNamed(context, '/therapist'),
                fullWidth: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Therapist Action Button Widget
  Widget _buildTherapistActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap, {
    bool fullWidth = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: fullWidth ? double.infinity : null,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
