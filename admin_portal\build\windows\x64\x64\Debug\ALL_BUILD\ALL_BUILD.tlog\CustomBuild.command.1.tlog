^C:\USERS\<USER>\DESKTOP\MINDEASE\ADMIN_PORTAL\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Mindease/admin_portal/windows -BC:/Users/<USER>/Desktop/Mindease/admin_portal/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/Mindease/admin_portal/build/windows/x64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
