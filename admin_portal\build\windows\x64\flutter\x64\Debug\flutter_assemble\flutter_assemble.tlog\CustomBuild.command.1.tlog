^C:\USERS\<USER>\DESKTOP\MINDEASE\ADMIN_PORTAL\BUILD\WINDOWS\X64\CMAKEFILES\04EC77E433FD329345B7B8D8A226EB45\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\Mindease\admin_portal FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\Mindease\admin_portal\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\Mindease\admin_portal FLUTTER_TARGET=C:\Users\<USER>\Desktop\Mindease\admin_portal\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Desktop\Mindease\admin_portal\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\MINDEASE\ADMIN_PORTAL\BUILD\WINDOWS\X64\CMAKEFILES\FD64BB51839D2A745F20F4D83D81625D\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\MINDEASE\ADMIN_PORTAL\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Mindease/admin_portal/windows -BC:/Users/<USER>/Desktop/Mindease/admin_portal/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/Mindease/admin_portal/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
